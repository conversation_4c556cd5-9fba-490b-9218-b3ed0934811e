import { Hono, type Context } from "hono";
import { db } from "../config/database";
import * as UAParser from "ua-parser-js";
import { successResponse, errorResponse } from "../utils/response.util";

const app = new Hono();

app.post("/", async (c) => {
  const body = await c.req.json();
  const existingSlug = await getLinkModelBySlug(body.slug);

  if (existingSlug) {
    return c.json(errorResponse("Slug already exists"), 400);
  }

  const slug = await generateUniqueSlug(body.slug);
  const link = await db.link.create({
    data: {
      slug,
      targetUrl: body.url,
    },
  });

  return c.json(successResponse({ shortUrl: `${c.req.url}/${link.slug}` }));
});

app.get("/:slug", async (c) => {
  const slug = c.req.param("slug");
  const link = await getLinkModelBySlug(slug);

  if (!link) return c.notFound();

  const userAgent = c.req.header("user-agent") || "";
  const parser = new UAParser.UAParser(userAgent);
  const uaResult = parser.getResult();
  const userIp = await getUserIp(c);

  await db.redirectUserInfo.create({
    data: {
      linkId: link.id,
      ip: userIp,
      browser: uaResult.browser.name || "unknown",
      os: uaResult.os.name || "unknown",
      device: uaResult.device.type || "Desktop",
    },
  });

  return c.redirect(link.targetUrl);
});

async function generateUniqueSlug(slug?: string) {
  let result = slug ?? Math.random().toString(36).substring(2, 8);

  while (await getLinkModelBySlug(result)) {
    result = Math.random().toString(36).substring(2, 8);
  }
  return result;
}

async function getLinkModelBySlug(slug: string) {
  if (!slug) return null;
  return await db.link.findUnique({ where: { slug } });
}

async function getUserIp(c: Context) {
const forwarded = c.req.raw.headers.get('x-forwarded-for')
  const realIp = c.req.raw.headers.get('x-real-ip')

  if (forwarded && typeof forwarded === 'string') {
    const firstForwarded = forwarded.split(',')[0];
    return firstForwarded ? firstForwarded.trim() : 'unknown';
  }
  if (realIp) return realIp

  const socket = (c.req.raw as any)?.socket
  if (socket?.remoteAddress) return socket.remoteAddress

  return 'unknown'
}

export const shortener = app;
