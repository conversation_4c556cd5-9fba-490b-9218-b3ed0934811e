import { createMiddleware } from 'hono/factory'
import { db } from '../config/database'

export const loggerMiddleware = createMiddleware(async (c, next) => {
  const start = Date.now()

  const method = c.req.method
  const url = c.req.url
  const headers = Object.fromEntries(c.req.raw.headers.entries())

  let requestBody: any = null
  if (['POST', 'PUT', 'PATCH'].includes(method)) {
    try {
      requestBody = await c.req.json()
    } catch (err) {
      requestBody = { error: 'Invalid or empty JSON body' }
    }
  }

  await next()

  const end = Date.now()
  const responseTimeMs = end - start

  const res = c.res
  let responseBody: any = null
  try {
    const clonedRes = res.clone()
    const text = await clonedRes.text()
    responseBody = JSON.parse(text)
  } catch (e) {
    responseBody = { error: 'Non-JSON response or parse error' }
  }

  await db.log.create({
    data: {
      url,
      method,
      headers,
      body: requestBody,
      response: responseBody,
      responseTimeMs,
    },
  })
})
