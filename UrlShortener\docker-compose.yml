services:
  # Backend Service (Bun.js)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: urlshortener_backend
    restart: unless-stopped
    environment:
      DATABASE_URL: "********************************************/urlshortener"
      PORT: 3000
    ports:
      - "3001:3000"  # ใช้ port 3001 เพื่อไม่ชนกับ services อื่น
    networks:
      - urlshortener_network
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: ["bun", "run", "dev"]

  # Frontend Service (Static Files)
  frontend:
    image: nginx:alpine
    container_name: urlshortener_frontend
    restart: unless-stopped
    ports:
      - "8081:80"  # ใช้ port 8081 เพื่อไม่ชนกับ services อื่น
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
    networks:
      - urlshortener_network

networks:
  urlshortener_network:
    driver: bridge
