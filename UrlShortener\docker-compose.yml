version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17.5
    container_name: urlshortener_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: urlshortener
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: mysecret
    ports:
      - "5433:5432"  # ใช้ port 5433 เพื่อไม่ชนกับ postgres หลัก
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - urlshortener_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d urlshortener"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend Service (Bun.js)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: urlshortener_backend
    restart: unless-stopped
    environment:
      DATABASE_URL: "********************************************/urlshortener"
      PORT: 3000
    ports:
      - "3001:3000"  # ใช้ port 3001 เพื่อไม่ชนกับ services อื่น
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - urlshortener_network
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: ["bun", "run", "dev"]

  # Frontend Service (Static Files)
  frontend:
    image: nginx:alpine
    container_name: urlshortener_frontend
    restart: unless-stopped
    ports:
      - "8081:80"  # ใช้ port 8081 เพื่อไม่ชนกับ services อื่น
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
    networks:
      - urlshortener_network

networks:
  urlshortener_network:
    driver: bridge
