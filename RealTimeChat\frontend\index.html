<!-- test.html -->
<input id="name" placeholder="name" />
<input id="msg" placeholder="message" />
<button onclick="send()">Send</button>
<pre id="log"></pre>

<script>
  const room = "global"; // หรือเปลี่ยนเป็นชื่ออื่น
  const socket = new WebSocket(`ws://localhost:3000/ws/${room}`);

  socket.onmessage = (e) => {
    const { sender, content } = JSON.parse(e.data);
    log(`[${sender}]: ${content}`);
  };

  function send() {
    const sender = document.getElementById("name").value;
    const content = document.getElementById("msg").value;
    socket.send(JSON.stringify({ sender, content }));
  }

  function log(msg) {
    document.getElementById("log").textContent += msg + "\n";
  }
</script>
