// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Link {
  id        Int      @id @default(autoincrement())
  slug      String   @unique
  targetUrl String
  createdAt DateTime @default(now())
  redirectUserInfo RedirectUserInfo[]
}

model RedirectUserInfo {
  link      Link     @relation(fields: [linkId], references: [id])
  linkId    Int
  id        Int      @id @default(autoincrement())
  ip        String
  browser   String
  os        String
  device    String
  dateTime  DateTime @default(now())
}

model Log {
  id        Int      @id @default(autoincrement())
  url       String
  method    String
  headers   Json
  body      Json?
  response  Json
  responseTimeMs Int
  createdAt DateTime @default(now())
}