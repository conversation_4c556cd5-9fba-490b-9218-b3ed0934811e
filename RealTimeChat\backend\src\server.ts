// file: src/server.ts
import { serve,type ServerWebSocket } from "bun";

// 👇 ใช้ type นี้เป็น context ที่แนบให้แต่ละ socket
type ChatData = {
  room: string;
};

// 👇 เก็บ socket แต่ละห้อง โดยใช้ type ที่ถูกต้อง
const rooms = new Map<string, Set<ServerWebSocket<ChatData>>>();

serve<ChatData, {}>({
  port: 3000,

  fetch(req, server) {
    const url = new URL(req.url);
    if (url.pathname.startsWith("/ws/")) {
      const room = url.pathname.split("/").pop();
      if (!room) return new Response("Room required", { status: 400 });

      // 👇 แนบ room เข้าไปใน data object
      if (server.upgrade(req, { data: { room } })) return;
      return new Response("Upgrade failed", { status: 400 });
    }

    return new Response("Bun WebSocket Chat Server ✅");
  },

  websocket: {
    open(ws) {
      const room = ws.data.room;
      if (!rooms.has(room)) rooms.set(room, new Set());
      rooms.get(room)!.add(ws);
      console.log(`[join] ${room}: ${rooms.get(room)!.size} client(s)`);
    },

    message(ws, message) {
      const room = ws.data.room;
      const peers = rooms.get(room);
      if (!peers) return;

      // 👇 broadcast ให้ทุกคนในห้อง ยกเว้น sender
      for (const client of peers) {
        if (client.readyState === WebSocket.OPEN) {
          client.send(message);
        }
      }
    },

    close(ws) {
      const room = ws.data.room;
      const peers = rooms.get(room);
      if (!peers) return;

      peers.delete(ws);
      if (peers.size === 0) {
        rooms.delete(room);
      }

      console.log(`[leave] ${room}: ${peers.size} client(s)`);
    },
  },
});
