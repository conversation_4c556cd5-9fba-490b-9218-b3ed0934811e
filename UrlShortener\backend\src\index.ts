import { Hono } from "hono";
import { serve } from "bun";
import { env } from "./config/env";
import { logger } from "hono/logger";
import { cors } from "hono/cors";
import { showRoutes } from "hono/dev";
import router from "./routes";
import { loggerMiddleware } from "./middleware/logger.middleware";
import rateLimit from "hono-rate-limit";

const app = new Hono();
app.use(logger());
app.use(cors());
app.use(loggerMiddleware);
app.use(
  "*",
  rateLimit({
    windowMs: 60 * 1000,
    limit: 10,
    message: "Too many requests, please try again later.",
  })
);

app.route("/api", router);

const port = env.PORT;
console.log(`Server is running on http://localhost:${port}`);

showRoutes(app);

serve({
  fetch: app.fetch,
  port,
});
