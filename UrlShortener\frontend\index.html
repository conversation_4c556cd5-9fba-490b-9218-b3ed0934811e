<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Shortener</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        input[type="url"], input[type="text"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input[type="url"]:focus, input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }

        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .short-url {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
        }

        .short-url input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }

        .copy-btn {
            padding: 10px 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 URL Shortener</h1>
            <p>สร้าง Short URL ง่ายๆ ด้วยระบบของเรา</p>
        </div>

        <form id="shortenForm">
            <div class="form-group">
                <label for="originalUrl">URL ที่ต้องการย่อ:</label>
                <input type="url" id="originalUrl" name="url" placeholder="https://example.com" required>
            </div>

            <div class="form-group">
                <label for="customSlug">Custom Slug (ไม่บังคับ):</label>
                <input type="text" id="customSlug" name="slug" placeholder="my-custom-link">
                <small style="color: #666; font-size: 12px;">หากไม่ระบุ ระบบจะสร้างให้อัตโนมัติ</small>
            </div>

            <button type="submit" class="btn" id="submitBtn">สร้าง Short URL</button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>กำลังสร้าง Short URL...</p>
        </div>

        <div class="result" id="result">
            <h3>ผลลัพธ์:</h3>
            <div class="short-url">
                <input type="text" id="shortUrlInput" readonly>
                <button class="copy-btn" onclick="copyToClipboard()">คัดลอก</button>
            </div>
        </div>

    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        
        document.getElementById('shortenForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            const originalUrl = document.getElementById('originalUrl').value;
            const customSlug = document.getElementById('customSlug').value;
            
            // Reset UI
            result.style.display = 'none';
            result.className = 'result';
            loading.style.display = 'block';
            submitBtn.disabled = true;
            
            try {
                const requestBody = {
                    url: originalUrl
                };
                
                if (customSlug.trim()) {
                    requestBody.slug = customSlug.trim();
                }
                
                const response = await fetch(`${API_BASE_URL}/short`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                
                loading.style.display = 'none';
                result.style.display = 'block';
                
                if (response.ok && data.success) {
                    result.className = 'result success';
                    document.getElementById('shortUrlInput').value = data.data.shortUrl;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `<h3>เกิดข้อผิดพลาด:</h3><p>${data.message || 'ไม่สามารถสร้าง Short URL ได้'}</p>`;
                }
                
            } catch (error) {
                loading.style.display = 'none';
                result.style.display = 'block';
                result.className = 'result error';
                result.innerHTML = `<h3>เกิดข้อผิดพลาด:</h3><p>ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้</p>`;
                console.error('Error:', error);
            } finally {
                submitBtn.disabled = false;
            }
        });
        
        function copyToClipboard() {
            const shortUrlInput = document.getElementById('shortUrlInput');
            shortUrlInput.select();
            shortUrlInput.setSelectionRange(0, 99999); // For mobile devices
            
            try {
                document.execCommand('copy');
                
                // Show feedback
                const copyBtn = event.target;
                const originalText = copyBtn.textContent;
                copyBtn.textContent = 'คัดลอกแล้ว!';
                copyBtn.style.background = '#28a745';
                
                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '#28a745';
                }, 2000);
                
            } catch (err) {
                console.error('Failed to copy: ', err);
                alert('ไม่สามารถคัดลอกได้ กรุณาคัดลอกด้วยตนเอง');
            }
        }
        
        // Test API connection on page load
        window.addEventListener('load', async function() {
            try {
                const response = await fetch(`${API_BASE_URL}`);
                if (response.ok) {
                    console.log('API connection successful');
                }
            } catch (error) {
                console.warn('API connection failed:', error);
            }
        });
    </script>
</body>
</html>
