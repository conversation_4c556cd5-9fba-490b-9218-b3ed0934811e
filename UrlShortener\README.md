# URL Shortener - Docker Setup

URL Shortener application ที่ใช้ Bun.js, PostgreSQL และ Nginx

## Services

- **Frontend**: Nginx serving static HTML (Port 8081)
- **Backend**: Bun.js API server (Port 3001)  
- **Database**: PostgreSQL (Port 5433)

## การใช้งาน

### 1. เตรียม Environment Variables

```bash
cp .env.example .env
```

### 2. รัน Docker Compose

```bash
# รัน services ทั้งหมด
docker-compose up -d

# ดู logs
docker-compose logs -f

# หยุด services
docker-compose down

# หยุดและลบ volumes (ข้อมูลจะหายหมด)
docker-compose down -v
```

### 3. Setup Database

```bash
# เข้าไปใน backend container
docker-compose exec backend bash

# รัน Prisma migrations
bunx prisma migrate dev

# หรือรันจากภายนอก container
docker-compose exec backend bunx prisma migrate dev
```

### 4. เข้าใช้งาน

- **Frontend**: http://localhost:8081
- **Backend API**: http://localhost:3001
- **Database**: localhost:5433

## การพัฒนา

### ดู logs แบบ real-time

```bash
# ดู logs ทั้งหมด
docker-compose logs -f

# ดู logs เฉพาะ service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
```

### เข้าไปใน container

```bash
# เข้าไปใน backend container
docker-compose exec backend bash

# เข้าไปใน database container
docker-compose exec postgres psql -U postgres -d urlshortener
```

### Rebuild services

```bash
# Rebuild backend เมื่อมีการเปลี่ยนแปลง Dockerfile
docker-compose build backend

# Rebuild และรันใหม่
docker-compose up -d --build
```

## Troubleshooting

### ปัญหา Port ชน

หาก port ชนกับ services อื่น สามารถแก้ไขใน `docker-compose.yml`:

```yaml
ports:
  - "8082:80"    # เปลี่ยนจาก 8081 เป็น 8082
  - "3002:3000"  # เปลี่ยนจาก 3001 เป็น 3002  
  - "5434:5432"  # เปลี่ยนจาก 5433 เป็น 5434
```

### ปัญหา Database Connection

ตรวจสอบว่า DATABASE_URL ใน backend ตรงกับการตั้งค่า postgres service

### ปัญหา Prisma

```bash
# Reset database
docker-compose exec backend bunx prisma migrate reset

# Generate client ใหม่
docker-compose exec backend bunx prisma generate
```
