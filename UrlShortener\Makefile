.PHONY: help up down logs build clean restart migrate shell db-shell

# Default target
help:
	@echo "Available commands:"
	@echo "  up        - Start all services"
	@echo "  down      - Stop all services"
	@echo "  logs      - Show logs for all services"
	@echo "  build     - Build all services"
	@echo "  clean     - Stop and remove all containers, networks, and volumes"
	@echo "  restart   - Restart all services"
	@echo "  migrate   - Run database migrations"
	@echo "  shell     - Open shell in backend container"
	@echo "  db-shell  - Open PostgreSQL shell"

# Start all services
up:
	docker-compose up -d

# Stop all services
down:
	docker-compose down

# Show logs
logs:
	docker-compose logs -f

# Build all services
build:
	docker-compose build

# Clean everything
clean:
	docker-compose down -v --remove-orphans
	docker system prune -f

# Restart all services
restart:
	docker-compose restart

# Run database migrations
migrate:
	docker-compose exec backend bunx prisma migrate dev

# Open shell in backend container
shell:
	docker-compose exec backend bash

# Open PostgreSQL shell
db-shell:
	docker-compose exec postgres psql -U postgres -d urlshortener
